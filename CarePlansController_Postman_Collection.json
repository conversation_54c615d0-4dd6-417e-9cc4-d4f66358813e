{"info": {"_postman_id": "care-plans-controller-collection", "name": "HMBL Core - Care Plans Controller", "description": "Complete Postman collection for CarePlansController with all routes including goals, interventions, problems, care team members, reviews, follow-ups, services, notes, and timeline items", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "memberID", "value": "123e4567-e89b-12d3-a456-426614174000", "type": "string"}, {"key": "carePlanID", "value": "123e4567-e89b-12d3-a456-426614174001", "type": "string"}, {"key": "goalID", "value": "123e4567-e89b-12d3-a456-426614174002", "type": "string"}, {"key": "interventionID", "value": "123e4567-e89b-12d3-a456-426614174003", "type": "string"}, {"key": "problemID", "value": "123e4567-e89b-12d3-a456-426614174004", "type": "string"}, {"key": "teamMemberID", "value": "123e4567-e89b-12d3-a456-426614174005", "type": "string"}, {"key": "reviewID", "value": "123e4567-e89b-12d3-a456-426614174006", "type": "string"}, {"key": "followUpID", "value": "123e4567-e89b-12d3-a456-426614174007", "type": "string"}, {"key": "serviceID", "value": "123e4567-e89b-12d3-a456-426614174008", "type": "string"}, {"key": "noteID", "value": "123e4567-e89b-12d3-a456-426614174009", "type": "string"}, {"key": "timelineItemID", "value": "123e4567-e89b-12d3-a456-426614174010", "type": "string"}, {"key": "teamID", "value": "123e4567-e89b-12d3-a456-426614174011", "type": "string"}], "item": [{"name": "Care Plans", "item": [{"name": "Create Care Plan", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Comprehensive Care Plan for Diabetes Management\",\n  \"startDate\": \"2025-01-01T00:00:00Z\",\n  \"lastReviewed\": \"2025-01-15T00:00:00Z\",\n  \"nextReviewDate\": \"2025-04-01T00:00:00Z\",\n  \"outcome\": \"Initial assessment completed\",\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/careplans", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "careplans"]}}}, {"name": "List Care Plans for Member", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/careplans", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "careplans"]}}}, {"name": "Get Care Plan", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}"]}}}, {"name": "Update Care Plan", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Comprehensive Care Plan for Diabetes Management\",\n  \"startDate\": \"2025-01-01T00:00:00Z\",\n  \"lastReviewed\": \"2025-01-20T00:00:00Z\",\n  \"nextReviewDate\": \"2025-05-01T00:00:00Z\",\n  \"outcome\": \"Progress noted, goals updated\",\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}"]}}}, {"name": "Delete Care Plan", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}"]}}}]}, {"name": "Goals", "item": [{"name": "Create Goal", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Medication Adherence Goal\",\n  \"description\": \"Improve medication adherence\",\n  \"type\": \"health\",\n  \"targetDate\": \"2025-06-01T00:00:00Z\",\n  \"status\": \"active\",\n  \"outcome\": null,\n  \"objective\": \"Patient will take prescribed medications as directed\",\n  \"measurementCriteria\": \"90% medication adherence rate\",\n  \"achievabilityNote\": \"Patient has shown willingness to improve\",\n  \"barriers\": \"Forgetfulness, complex medication schedule\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/goals", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "goals"]}}}, {"name": "List Goals", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/goals", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "goals"]}}}, {"name": "Update Goal", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Enhanced Medication Adherence Goal\",\n  \"description\": \"Improve medication adherence to 95%\",\n  \"type\": \"health\",\n  \"targetDate\": \"2025-07-01T00:00:00Z\",\n  \"status\": \"in_progress\",\n  \"outcome\": \"Patient showing improvement\",\n  \"objective\": \"Patient will take prescribed medications as directed\",\n  \"measurementCriteria\": \"95% medication adherence rate\",\n  \"achievabilityNote\": \"Patient has shown willingness to improve\",\n  \"barriers\": \"Reduced barriers with pill organizer\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/goals/{{goalID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "goals", "{{goalID}}"]}}}, {"name": "Delete Goal", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/goals/{{goalID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "goals", "{{goalID}}"]}}}]}, {"name": "Interventions", "item": [{"name": "Create Intervention", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"Schedule weekly medication review\",\n  \"responsibleParty\": \"Primary Care Nurse\",\n  \"dueDate\": \"2025-02-01T00:00:00Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/interventions", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "interventions"]}}}, {"name": "List Interventions", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/interventions", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "interventions"]}}}, {"name": "Update Intervention", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"Schedule bi-weekly medication review\",\n  \"responsibleParty\": \"Primary Care Nurse\",\n  \"dueDate\": \"2025-02-15T00:00:00Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/interventions/{{interventionID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "interventions", "{{interventionID}}"]}}}, {"name": "Delete Intervention", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/interventions/{{interventionID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "interventions", "{{interventionID}}"]}}}]}, {"name": "Problems", "item": [{"name": "Create Problem", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"I10\",\n  \"description\": \"Essential (primary) hypertension\",\n  \"clinicalNote\": \"BP remains elevated despite medication adjustment.\",\n  \"status\": \"active\",\n  \"dateIdentified\": \"2023-06-01T00:00:00Z\",\n  \"source\": \"EHR import\",\n  \"confirmedBy\": \"<PERSON><PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/problems", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "problems"]}}}, {"name": "List Problems", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/problems", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "problems"]}}}, {"name": "Update Problem", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"I10\",\n  \"description\": \"Essential (primary) hypertension\",\n  \"clinicalNote\": \"BP now controlled with current medication regimen. Patient showing good compliance.\",\n  \"status\": \"resolved\",\n  \"dateIdentified\": \"2023-06-01T00:00:00Z\",\n  \"source\": \"EHR import\",\n  \"confirmedBy\": \"Dr<PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/problems/{{problemID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "problems", "{{problemID}}"]}}}, {"name": "Delete Problem", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/problems/{{problemID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "problems", "{{problemID}}"]}}}]}, {"name": "Care Team Members", "item": [{"name": "Create Care Team Member", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userID\": \"123e4567-e89b-12d3-a456-426614174009\",\n  \"name\": \"Dr. <PERSON>\",\n  \"role\": \"Primary Care Physician\",\n  \"contactInfo\": \"<EMAIL>, (555) 123-4567\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/team-members", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "team-members"]}}}, {"name": "List Care Team Members", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/team-members", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "team-members"]}}}, {"name": "Add Team Members from Team", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"teamID\": \"{{teamID}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/team-members/from-team", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "team-members", "from-team"]}}}, {"name": "Update Care Team Member", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userID\": \"123e4567-e89b-12d3-a456-426614174009\",\n  \"name\": \"Dr. <PERSON>, MD\",\n  \"role\": \"Primary Care Physician\",\n  \"contactInfo\": \"<EMAIL>, (555) 123-4567, ext. 101\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/team-members/{{teamMemberID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "team-members", "{{teamMemberID}}"]}}}, {"name": "Delete Care Team Member", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/team-members/{{teamMemberID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "team-members", "{{teamMemberID}}"]}}}]}, {"name": "Reviews", "item": [{"name": "Create Care Plan Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewDate\": \"2025-01-20T00:00:00Z\",\n  \"notes\": \"Patient showing good progress on medication adherence. Goals remain appropriate.\",\n  \"reviewerName\": \"Dr. <PERSON>\",\n  \"reviewerRole\": \"Primary Care Physician\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/reviews", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "reviews"]}}}, {"name": "List Care Plan Reviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/reviews", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "reviews"]}}}, {"name": "Update Care Plan Review", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewDate\": \"2025-01-20T00:00:00Z\",\n  \"notes\": \"Patient showing excellent progress on medication adherence. Goals updated to reflect new targets.\",\n  \"reviewerName\": \"Dr. <PERSON>\",\n  \"reviewerRole\": \"Primary Care Physician\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/reviews/{{reviewID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "reviews", "{{reviewID}}"]}}}, {"name": "Delete Care Plan Review", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/reviews/{{reviewID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "reviews", "{{reviewID}}"]}}}]}, {"name": "Follow-ups", "item": [{"name": "Create Follow-up", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"datetime\": \"2025-01-25T14:30:00Z\",\n  \"type\": \"Phone\",\n  \"outcome\": \"Reached\",\n  \"notes\": \"Patient confirmed medication adherence improving. Scheduled next appointment.\",\n  \"staffName\": \"Nurse <PERSON>\",\n  \"staffRole\": \"Care Coordinator\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/followups", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "followups"]}}}, {"name": "List Follow-ups", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/followups", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "followups"]}}}, {"name": "Get Follow-up", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/followups/{{followUpID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "followups", "{{followUpID}}"]}}}, {"name": "Update Follow-up", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"datetime\": \"2025-01-25T14:30:00Z\",\n  \"type\": \"Phone\",\n  \"outcome\": \"Reached\",\n  \"notes\": \"Patient confirmed medication adherence at 95%. Very positive progress. Next follow-up in 2 weeks.\",\n  \"staffName\": \"<PERSON>\",\n  \"staffRole\": \"Care Coordinator\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/followups/{{followUpID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "followups", "{{followUpID}}"]}}}, {"name": "Delete Follow-up", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/followups/{{followUpID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "followups", "{{followUpID}}"]}}}]}, {"name": "Services", "item": [{"name": "Create Care Plan Service", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cboName\": \"Community Health Partners\",\n  \"staffName\": \"<PERSON>\",\n  \"addedBy\": \"Dr. <PERSON>\",\n  \"status\": \"pending\",\n  \"appointmentDate\": \"2025-02-05T10:00:00Z\",\n  \"outcomeReasonType\": null,\n  \"outcomeReasonDescription\": null\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/services", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "services"]}}}, {"name": "List Care Plan Services", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/services", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "services"]}}}, {"name": "Get Care Plan Service", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/services/{{serviceID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "services", "{{serviceID}}"]}}}, {"name": "Update Care Plan Service", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cboName\": \"Community Health Partners\",\n  \"staffName\": \"<PERSON>\",\n  \"addedBy\": \"Dr. <PERSON>\",\n  \"status\": \"completed\",\n  \"appointmentDate\": \"2025-02-05T10:00:00Z\",\n  \"outcomeReasonType\": \"successful\",\n  \"outcomeReasonDescription\": \"Patient attended appointment and received nutrition counseling\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/services/{{serviceID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "services", "{{serviceID}}"]}}}, {"name": "Delete Care Plan Service", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/services/{{serviceID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "services", "{{serviceID}}"]}}}]}, {"name": "Notes", "item": [{"name": "Create Care Plan Note", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Patient Progress Update\",\n  \"type\": \"progress\",\n  \"subtitle\": \"Weekly Assessment\",\n  \"msg\": \"Patient showing significant improvement in mobility exercises. Able to walk 50 feet without assistance.\",\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/notes", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "notes"]}}}, {"name": "Create Assessment Note", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Monthly Care Assessment\",\n  \"type\": \"assessment\",\n  \"subtitle\": \"Comprehensive Review\",\n  \"msg\": \"Patient demonstrates good understanding of care plan goals. Blood pressure readings have improved significantly over the past month. Medication adherence at 95%.\",\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/notes", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "notes"]}}}, {"name": "Create Communication Note", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Family Communication\",\n  \"type\": \"communication\",\n  \"subtitle\": \"Spoke with daughter\",\n  \"msg\": \"Discussed patient's progress with daughter <PERSON>. She reports patient is more active at home and following medication schedule. Requested copy of care plan updates.\",\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/notes", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "notes"]}}}, {"name": "List Care Plan Notes", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/notes", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "notes"]}}}, {"name": "Get Care Plan Note", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/notes/{{noteID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "notes", "{{noteID}}"]}}}, {"name": "Update Care Plan Note", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Progress Note\",\n  \"type\": \"progress\",\n  \"subtitle\": \"Weekly Assessment - Updated\",\n  \"msg\": \"Patient continues to show excellent improvement. Now able to walk 75 feet without assistance and reports reduced pain levels.\",\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/notes/{{noteID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "notes", "{{noteID}}"]}}}, {"name": "Delete Care Plan Note", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/notes/{{noteID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "notes", "{{noteID}}"]}}}]}, {"name": "Timeline Items", "item": [{"name": "Create Timeline Item", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"carepackageID\": \"care-plan-timeline\",\n  \"title\": \"Care Plan Review Scheduled\",\n  \"status\": \"scheduled\",\n  \"desc\": \"Quarterly care plan review scheduled with care team\",\n  \"visible\": true,\n  \"memberId\": \"{{memberID}}\",\n  \"meta\": {\n    \"data\": {\n      \"review_type\": \"quarterly\",\n      \"priority\": \"normal\"\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/timeline-items", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "timeline-items"]}}}, {"name": "List Timeline Items", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/timeline-items", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "timeline-items"]}}}, {"name": "Get Timeline Item", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/timeline-items/{{timelineItemID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "timeline-items", "{{timelineItemID}}"]}}}, {"name": "Update Timeline Item", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"carepackageID\": \"care-plan-timeline\",\n  \"title\": \"Care Plan Review Completed\",\n  \"status\": \"completed\",\n  \"desc\": \"Quarterly care plan review completed successfully. Goals updated.\",\n  \"visible\": true,\n  \"memberId\": \"{{memberID}}\",\n  \"meta\": {\n    \"data\": {\n      \"review_type\": \"quarterly\",\n      \"priority\": \"normal\",\n      \"outcome\": \"goals_updated\"\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/timeline-items/{{timelineItemID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "timeline-items", "{{timelineItemID}}"]}}}, {"name": "Delete Timeline Item", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/timeline-items/{{timelineItemID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "timeline-items", "{{timelineItemID}}"]}}}]}]}