//
//  File.swift
//
//
//  Created by <PERSON> on 7/12/24.
//

import Foundation
import Vapor
import Fluent
import Leaf
import SwiftCSV
import SotoSNS
import SotoS3
import ZIPFoundation

struct ExportResponse: Content {
    var response: ClientResponse
    var urls: [URL]
    var downloadLink:String
}

struct ExportData {

    static let env:String = isProduction ? "production" : "staging"
    var model:ReportQuery
    var paths: [URL] = []
    let config: ReportConfiguration


    //    func allData(req: Request) throws -> EventLoopFuture<ClientResponse> {
    //        let input = try req.content.decode(ReportRequest.self)
    //    }
    //
    func exportData(req: Request) throws -> EventLoopFuture<ExportResponse> { //[URL]
        var localPaths = self.paths

        return try! activeMembers(req: req).flatMap({ response in
            localPaths.append(contentsOf: response.urls)
            return try! allMembers(req: req).flatMap({ response in
                localPaths.append(contentsOf: response.urls)

                return try! newEnrollment(req: req).flatMap({ response in
                    localPaths.append(contentsOf: response.urls)
                    return try! notActiveMembers(req: req).flatMap({ response in
                        localPaths.append(contentsOf: response.urls)
                        return try! answers(req: req).flatMap({ response in
                            localPaths.append(contentsOf: response.urls)
                            return try! questions(req: req).flatMap({ response in
                                localPaths.append(contentsOf: response.urls)
                                return try! exportPlans(req: req).flatMap({ response in
                                    localPaths.append(contentsOf: response.urls)
                                    return try! totalServices(req: req).flatMap({ response in
                                        localPaths.append(contentsOf: response.urls)
                                        return try! insurance(req: req).flatMap({ response in
                                            localPaths.append(contentsOf: response.urls)
                                            return try! notes(req: req).flatMap({ response in
                                            localPaths.append(contentsOf: response.urls)
                                            return try! assessments(req: req).flatMap({ response in
                                                localPaths.append(contentsOf: response.urls)
                                                return try! appointments(req: req).flatMap({ response in
                                                    localPaths.append(contentsOf: response.urls)
                                                    return try! chats(req: req).flatMap { response in
                                                        localPaths.append(contentsOf: response.urls)

                                                        return try! questionsCombined(req: req).flatMap { response in
                                                            localPaths.append(contentsOf: response.urls)

                                                            return try! tasks(req: req).flatMap { response in
                                                                localPaths.append(contentsOf: response.urls)

                                                                return try! memberAttachments(req: req).flatMap({ response in
                                                                    localPaths.append(contentsOf: response.urls)

                                                                    return try! memberConsents(req: req).flatMap({ response in
                                                                        localPaths.append(contentsOf: response.urls)

                                                                        return try! createZipFile(req: req, fileName: config.compressFileName(), from: localPaths).flatMap { response in
                                                                            print(response)
                                                                            return uploadToS3(req: req, zipFileURL: response, config: config, shutdown: false).flatMap { response in
                                                                                var data = response
                                                                                return getDownloadFolder(req: req, config: config).flatMap({ downloadlink in
                                                                                    data.downloadLink = downloadlink
                                                                                    return req.eventLoop.future(data)
                                                                                })
                                                                            }
                                                                        }
                                                                    })
                                                                })
                                                            }
                                                        }
                                                    }
                                                })
                                            })
                                        })
                                    })
                                })
                            })
                        })
                        })
                    })
                })
            })
        })
    }

    func tasks(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let date = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)
        return TaskModel.query(on: req.db)
            .filter(\.$createdAt >= date)
            .filter(\.$createdAt <= endDate)
            .filter(\.$org.$id == UUID(model.org)!)
            .with(\.$assignee)
            .with(\.$receivers,{ member in
                member.with(\.$households, { household in
                    household.with(\.$teams)
                })
            })
            .with(\.$creator)
            .with(\.$reason)
            .all()
            .flatMap { tasks  in
                let rows = tasks.map({ TaskCSV.row(task: $0)}).joined()
                let csvText = "\(TaskCSV.headers())\(rows)"
                let fileName = "tasks"
                return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
            }
    }



    func chats(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let date = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)
        return Chat.query(on: req.db)
            .filter(\.$createdAt >= date)
            .filter(\.$createdAt <= endDate)
            .filter(\.$org.$id == UUID(input.org)!)
            .with(\.$creator)
            .all().flatMap { chats in
                let rows = chats.map({ChatsCSV.row(chat: $0)}).joined()
                let csvText = "\(ChatsCSV.headers())\(rows)"
                let fileName = "chats"
                return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
            }
    }

    func appointments(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let date = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)
        return Appointment.query(on: req.db)
            .filter(\.$createdAt >= date)
            .filter(\.$createdAt <= endDate)
            .filter(\.$org.$id == UUID(input.org)!)
            .with(\.$member, { member in
                member.with(\.$households, { household in
                    household.with(\.$teams)
                })
            })
            .with(\.$host)
            .all().flatMap { appointments in
                let rows = appointments.map({AppointmentsCSV.row(appointment: $0)}).joined()
                let csvText = "\(AppointmentsCSV.headers())\(rows)"
                let fileName = "appointments"
                return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
            }
    }

    func assessments(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let date = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)
        return Survey.query(on: req.db)
            .filter(\.$createdAt >= date)
            .filter(\.$createdAt <= endDate)
            .group(.or) { status in
                status.filter(\.$orgID == input.org.lowercased())
                status.filter(\.$orgID == input.org.uppercased())
            }
            .all().flatMap { surveys in
                let rows = surveys.map({SurveyCSV.row(survey: $0)}).joined()
                let csvText = "\(SurveyCSV.headers())\(rows)"
                let fileName = "surveys"
                return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
            }
    }

    func notes(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let date = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)
        return User.query(on: req.db)
            .filter(\.$org.$id == UUID(input.org)!)
            .all(\.$id).flatMap { userIds in
                return Note.query(on: req.db)
                    .filter(\.$createdAt >= date)
                    .filter(\.$createdAt <= endDate)
                    .filter(\.$creator.$id ~~ userIds)
                    .with(\.$tags)
                    .with(\.$member, { member in
                        member.with(\.$households, { household in
                            household.with(\.$teams)
                        })
                    })
                    .with(\.$creator)
                    .all().flatMap { notes in
                        let rows = notes.map({NoteCSV.row(note: $0)}).joined()
                        let csvText = "\(NoteCSV.headers())\(rows)"
                        let fileName = "notes"
                        return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
                    }
            }
    }

    func activeMembers(req: Request) throws -> EventLoopFuture<ExportResponse> {
        //        let input = try req.content.decode(ReportRequest.self)
        let input = try req.content.decode(ReportInput.self)
        return Member.query(on: req.db)
            .filter(\.$status == "active")
            .group(.or) { orGroup in
                orGroup.filter(\.$unenrolledDate == "")
                orGroup.filter(\.$unenrolledDate == nil)
            }
            .with(\.$phones)
            .with(\.$households, { house in
                house.with(\.$address)
                house.with(\.$teams)
            })
            .filter(\.$org.$id == UUID(input.org)!)
            .all().flatMap { members in
                let rows = members.map({CSVCreator().memberRowWithAddressAnTeamAndPhone(member: $0)}).joined()
                let csvText = "\(MemberWithAddressWithTeamWithPhoneNumber.headers())\(rows)"
                let fileName = "active_members"
                return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
            }
    }

    func newEnrollment(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        return Member.query(on: req.db)
            .filter(\.$status == "active")
            .filter(\.$enrolledOn >= input.startDate)
            .filter(\.$enrolledOn <= input.endDate)
            .group(.or) { orGroup in
                orGroup.filter(\.$unenrolledDate == "")
                orGroup.filter(\.$unenrolledDate == nil)
            }
            .with(\.$phones)
            .with(\.$households, { house in
                house.with(\.$address)
                house.with(\.$teams)
            })
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(input.org)!)
            .all().flatMap { members in
                let rows = members.map({CSVCreator().memberRowWithAddressAnTeamAndPhone(member: $0)}).joined()
                let csvText = "\(MemberWithAddressWithTeamWithPhoneNumber.headers())\(rows)"
                let fileName = "new_enrollments"
                return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
            }
    }

    func notActiveMembers(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        return Member.query(on: req.db)
            .filter(\.$status != "active")
            .filter(\.$enrolledOn >= input.startDate)
            .filter(\.$enrolledOn <= input.endDate)
            .group(.or) { orGroup in
                orGroup.filter(\.$unenrolledDate != "")
                orGroup.filter(\.$unenrolledDate != nil)
            }
            .with(\.$phones)
            .with(\.$households, { house in
                house.with(\.$address)
                house.with(\.$teams)
            })
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(input.org)!)
            .all().flatMap { members in
                let rows = members.map({CSVCreator().memberRowWithAddressAnTeamAndPhone(member: $0)}).joined()
                let csvText = "\(MemberWithAddressWithTeamWithPhoneNumber.headers())\(rows)"
                let fileName = "discharge"
                return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
            }
    }

    func memberConsents(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let startDate = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)

        guard let orgID = UUID(uuidString: input.org) else {
            throw Abort(.badRequest, reason: "Invalid organization ID")
        }

        return Member.query(on: req.db)
            .filter(\.$createdAt >= startDate)
            .filter(\.$createdAt <= endDate)
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == orgID)
            .all()
            .flatMap { members in
                let futures: [EventLoopFuture<String>] = members.map { member in
                    do {
                        return try self.generateConsentCSVRows(req: req, member: member)
                    } catch {
                        return req.eventLoop.makeFailedFuture(error)
                    }
                }

                // Combine all row strings into one, then upload
                return futures.flatten(on: req.eventLoop).flatMapThrowing { rowsArray in
                    let allRows = rowsArray.joined()
                    let csvText = ConsentCSV.headers() + allRows
                    let fileName = "all-consents.csv"
                    return try uploadData(req: req, fileName: fileName, csv: csvText, config: config)
                }.flatMap { $0 }
            }
    }

    func generateConsentCSVRows(req: Request, member: Member) throws -> EventLoopFuture<String> {
        return Consent.query(on: req.db)
            .filter(\.$memberId == member.id)
            .all()
            .map { consents in
                var rows = ""
                let memberId = member.id?.uuidString ?? ""
                let memberName = member.fullName()
                for consent in consents {
                    rows += ConsentCSV.row(consent: consent, memberId: memberId, memberName: memberName)
                }
                return rows
            }
    }

    func memberAttachments(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let startDate = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)
        return Member.query(on: req.db)
            .filter(\.$createdAt >= startDate)
            .filter(\.$createdAt <= endDate)
            .with(\.$attachments)
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(input.org)!)
            .all()
            .flatMapThrowing { members in
                var allRows = ""

                for member in members {
                    let memberId = member.id?.uuidString ?? ""
                    let memberName = member.fullName() // Adjust based on your model
                    for attachment in member.attachments {
                        allRows += AttachmentCSV.row(attachment: attachment, memberId: memberId, memberName: memberName)
                    }
                }

                let csvText = AttachmentCSV.headers() + allRows
                let fileName = "attachments"
                return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
            }
            .flatMap { $0 }
    }

    //    func exportAttachments(req: Request) throws -> EventLoopFuture<ClientResponse> {
    //        return Attachment.query(on: req.db)
    //            .filter(\.$createdAt >= model.start)
    //            .filter(\.$createdAt <= model.end)
    //            .filter(\.$org.$id == UUID(model.org)!)
    //            .all().flatMap { notes in
    //                let rows = notes.map({ AttachmentCSV.row(attachment: $0)}).joined()
    //                let csvText = "\(AttachmentCSV.headers())\(rows)"
    //                let fileName = "attachments"
    //                return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
    //            }
    //    }

    func allMembers(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let startDate = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)
        return Member.query(on: req.db)
            .filter(\.$createdAt >= startDate)
            .filter(\.$createdAt <= endDate)
            .with(\.$phones)
            .with(\.$households, { house in
                house.with(\.$address)
                house.with(\.$teams)
            })
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(input.org)!)
            .all().flatMap { members in
                let rows = members.map({CSVCreator().memberRowWithAddressAnTeamAndPhone(member: $0)}).joined()
                let csvText = "\(MemberWithAddressWithTeamWithPhoneNumber.headers())\(rows)"
                let fileName = "all_members"
                return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
            }
    }

    func answers(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let date = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)
        return Answer.query(on: req.db)
            .filter(\.$createdAt >= date)
            .filter(\.$createdAt <= endDate)
            .group(.or) { status in
                status.filter(\.$orgID == input.org.lowercased())
                status.filter(\.$orgID == input.org.uppercased())
            }
            .all().flatMap { answers in
                let rows = answers.map({CSVCreator().answerRow(answer: $0, newLine: false)}).joined()
                let csvText = "\(AnswersHeaders.headers())\(rows)"
                let fileName = "answers"
                return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
            }
    }

    func questionsCombined(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let start = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let end = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)

        return Answer.query(on: req.db)
            .filter(\.$createdAt >= start)
            .filter(\.$createdAt <= end)
            .group(.or) { status in
                status.filter(\.$orgID == input.org.lowercased())
                status.filter(\.$orgID == input.org.uppercased())
            }
            .all()
            .flatMap { answers in
                let questionIds = answers.compactMap { $0.questionID }
                let parentIds = answers.compactMap { $0.parentID }
                let allQuestionIds = Set(questionIds + parentIds).compactMap { UUID(uuidString: $0) }

                return Question.query(on: req.db)
                    .filter(\.$id ~~ allQuestionIds)
                    .with(\.$section) { section in
                        section.with(\.$survey) // eager load survey within section
                    }
                    .with(\.$question){ parentQuestion in
                        parentQuestion.with(\.$section) { section in
                            section.with(\.$survey) // eager load survey within section
                        }
                    }
                    .all()
                    .flatMap { questions in
                        let questionMap: [String: Question] = Dictionary(uniqueKeysWithValues: questions.compactMap { q in
                            guard let id = q.id else { return nil }
                            return (id.uuidString.lowercased(), q)
                        })

                        let rows = answers.flatMap { answer -> [String] in
                            var outputRows: [String] = []

                            let questionFromQuestionID = answer.questionID.flatMap { questionMap[$0.lowercased()] }
                            let questionFromParentID = answer.parentID.flatMap { questionMap[$0.lowercased()] }

                            // If both exist and are different
                            if let q1 = questionFromQuestionID, let q2 = questionFromParentID, q1.id != q2.id {
                                outputRows.append(CSVCreator().combinedAnswerQuestionRow(answer: answer, question: q1))
                                outputRows.append(CSVCreator().combinedAnswerQuestionRow(answer: answer, question: q2))
                            }
                            // If only one exists or both are the same
                            else if let q = questionFromQuestionID ?? questionFromParentID {
                                outputRows.append(CSVCreator().combinedAnswerQuestionRow(answer: answer, question: q))
                            } else {
                                // Handle if neither question was found (optional)
                                outputRows.append(CSVCreator().combinedAnswerQuestionRow(answer: answer, question: nil))
                            }

                            return outputRows
                        }.joined()

                        let csvText = "\(QuestionHeaders.combinedHeaders())\(rows)"

                        do {
                            return try uploadData(req: req, fileName: "questions_and_answers", csv: csvText, config: config)
                        } catch {
                            return req.eventLoop.makeFailedFuture(error)
                        }
                    }
            }
    }

    func questions(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let date = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)
        return Answer.query(on: req.db)
            .filter(\.$createdAt >= date)
            .filter(\.$createdAt <= endDate)
            .group(.or) { status in
                status.filter(\.$orgID == input.org.lowercased())
                status.filter(\.$orgID == input.org.uppercased())
            }
            .all()
            .flatMap { answersInRange in
                // Collect all question and parent IDs
                let questionIds = answersInRange.compactMap { $0.questionID }
                let parentIds = answersInRange.compactMap { $0.parentID }
                let allQuestionIds = Set(questionIds + parentIds).compactMap { UUID(uuidString: $0) }

                return Question.query(on: req.db)
                    .filter(\.$id ~~ allQuestionIds)
                    .with(\.$section)
                    .with(\.$question)
                    .all().flatMap { questions in
                        let rows = questions.map({CSVCreator().questionRow(question: $0)}).joined()
                        let csvText = "\(QuestionHeaders.headers())\(rows)"
                        let fileName = "questions"
                        return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
                    }
            }
    }

    func exportPlans(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let startDate = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)
        return CarePackage.query(on: req.db)
            .filter(\.$createdAt >= startDate)
            .filter(\.$createdAt <= endDate)
            .filter(\.$org.$id == UUID(model.org)!)
            .with(\.$reason)
            .all().flatMap { plans in
                let rows = plans.map({ CarePackageCSV.row(plan: $0)}).joined()
                let csvText = "\(CarePackageCSV.headers())\(rows)"
                let fileName = "social_plans"
                return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
                //                return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
            }
    }

    func totalServices(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let startDate = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)
        return CarePackage.query(on: req.db)
            .filter(\.$createdAt >= startDate)
            .filter(\.$createdAt <= endDate)
            .filter(\.$org.$id == UUID(model.org)!)
            .all(\.$id).flatMap { cpIds  in
                return CarePackageItem.query(on: req.db)
                    .with(\.$network)
                    .with(\.$carepackage)
                    .filter(\.$carepackage.$id ~~ cpIds)
                    .all().flatMap { plans in
                        let rows = plans.map({ CarePackageItemCSV.row(item: $0)}).joined()
                        let csvText = "\(CarePackageItemCSV.headers())\(rows)"
                        let fileName = "services"
                        return try! uploadData(req: req, fileName: fileName, csv: csvText, config: config)
                    }
            }
    }

    func insurance(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let startDate = Date.yearMonthDayDateWithDashFromString(dateString: input.startDate)
        let endDate = Date.yearMonthDayDateWithDashFromString(dateString: input.endDate)
        return InsurancePolicy.query(on: req.db)
            .filter(\.$createdAt >= startDate)
            .filter(\.$createdAt <= endDate)
            .with(\.$plans)
            .all().flatMap { policies in
                let policyRows = policies.map({ InsurancePolicyCSV.row(policy: $0)}).joined()
                let policyCsvText = "\(InsurancePolicyCSV.headers())\(policyRows)"
                let policyFileName = "insurance_policies"

                // Get all plans from the policies
                let allPlans = policies.flatMap { $0.plans }
                let planRows = allPlans.map({ InsurancePlanCSV.row(plan: $0)}).joined()
                let planCsvText = "\(InsurancePlanCSV.headers())\(planRows)"
                let planFileName = "insurance_plans"

                // Upload policy data first
                return try! uploadData(req: req, fileName: policyFileName, csv: policyCsvText, config: config).flatMap { policyResponse in
                    // Then upload plan data
                    return try! uploadData(req: req, fileName: planFileName, csv: planCsvText, config: config)
                }
            }
    }

    func rootDirectory(app:Application) -> URL {
        return URL(fileURLWithPath: app.directory.publicDirectory +  "csv/", isDirectory: true)
    }

    func createZipFile(req:Request, fileName:String, from fileURLs: [URL]) throws -> EventLoopFuture<URL> {
        let tempDir = self.rootDirectory(app: req.application)
        let zipFileURL = tempDir.appendingPathComponent("\(fileName).zip")
        let fileManager = FileManager.default
        let archive = try Archive(url: zipFileURL, accessMode: .create)

        for fileURL in fileURLs {
            try archive.addEntry(with: fileURL.lastPathComponent, relativeTo: tempDir)
            if fileManager.fileExists(atPath: fileURL.relativePath) {
                try fileManager.removeItem(atPath: fileURL.relativePath)
            }
        }
        req.logger.info("SAVE zipFileURL: \(zipFileURL)")
        req.logger.warning("SAVE zipFileURL: \(zipFileURL)")
        return req.eventLoop.future(zipFileURL)
    }

    func uploadToS3(req:Request, zipFileURL: URL, config: ReportConfiguration, shutdown: Bool = true) -> EventLoopFuture<ExportResponse> {
        let cloudWatchLogger = CloudWatchLogger(req: req, logGroupName: .reporting)
        req.logger.info("GET zipFileURL: \(zipFileURL)")
        req.logger.warning("GET zipFileURL: \(zipFileURL)")
        return req.fileio.collectFile(at: zipFileURL.relativePath).flatMap { buffer in
            return sendToAPIGateway(req: req, jsonData: buffer, fileName: "\(config.folder()).zip", config: config).flatMap { response in

                let fileManager = FileManager.default

                if fileManager.fileExists(atPath: zipFileURL.relativePath) {
                    try? fileManager.removeItem(atPath: zipFileURL.relativePath)
                }

                let msg = CloudWatchLogMessage.send(msg: .reportExport(bucket: config.bucket,
                                                                       endpoint: config.endpoint,
                                                                       status: response.status.reasonPhrase,
                                                                       name: "\(config.folder()).zip"))

                return cloudWatchLogger.putLog(message: msg, on: req.eventLoop).flatMap({ _ in
                    if shutdown {
                        cloudWatchLogger.shutdown()
                    }

                    return req.eventLoop.future(
                        ExportResponse(response: response, urls: [], downloadLink: "")
                    )
                })
            }
        }
    }

    func uploadMembersData(fileName: String, req:Request, members: [Member]) throws -> EventLoopFuture<[URL]> {
        let rows = members.map({CSVCreator().memberRowWithAddress(member: $0)}).joined()
        let csvText = "\(MemberWithAddress.headers())\(rows)"
        let fileName = fileName

        return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, filePaths: paths)
        //        return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
    }

    func uploadData(req: Request, fileName: String, csv:String, config: ReportConfiguration) throws -> EventLoopFuture<ExportResponse> {
        if config.exportAll {
            return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csv, req: req, isEmpty: false, filePaths: paths).flatMap({ urls in
                return req.eventLoop.makeSucceededFuture(
                    ExportResponse(response: ClientResponse(status: .accepted),
                                   urls: urls, downloadLink: "")
                )
            })
        } else {
            return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csv, req: req, isEmpty: false, config: config).flatMap({ response in
                return req.eventLoop.makeSucceededFuture(
                    ExportResponse(response: ClientResponse(status: .accepted),
                                   urls: [], downloadLink: "")
                )
            })
        }
    }

    func sendToAPIGateway(req:Request, jsonData:ByteBuffer?, fileName:String, config: ReportConfiguration) -> EventLoopFuture<ClientResponse> {
        guard let data = jsonData else {
            return req.eventLoop.future(ClientResponse(status: .badRequest))
        }

        if config.isWellup() {
            let s3 = S3(client: req.aws.client, region: .useast1)

            return s3.putObject(.init(
                body:.byteBuffer(data),
                bucket: config.bucket,
                key: fileName)).flatMap { bucket in
                    return req.eventLoop.future(ClientResponse(status: .accepted))
                }
        }
        else {
            return sendToAPIGatewayREST(req: req, jsonData: jsonData, fileName: fileName, config: config)
        }
    }

    func getDownloadFolder(req:Request, config: ReportConfiguration) -> EventLoopFuture<String> {
        let s3 = S3(client: req.aws.client, region: .useast1)
        req.logger.info("Download link: \(config.downloadLink())")
        return s3.signURL(url: URL(string: config.downloadLink())!,
                          httpMethod: .GET, expires: .hours(24)).map { url in
            return url.absoluteString
        }
    }

    fileprivate func sendToAPIGatewayREST(req:Request, jsonData:ByteBuffer?, fileName:String, config: ReportConfiguration) -> EventLoopFuture<ClientResponse> {
        guard let data = jsonData else {
            return req.eventLoop.future(ClientResponse(status: .badRequest))
        }

        let client = req.client
        let url = queryURL(urlString: "\(config.endpointWithBucket())/\(fileName)")
        let uri = URI(string: url!)

        return client.put(uri, headers: [
            "Content-type": "application/zip",
        ], beforeSend: { req in
            req.body = data //ByteBuffer.init(data: data)
        }).map { response in
            print(response)
            return response
        }
    }
}


